# RAG 文档索引 API 文档

## 📋 API 概览

**基础信息**

- **服务地址**: `http://localhost:8000`
- **API 版本**: `/api/v1`
- **文档地址**:
  - Swagger UI: `http://localhost:8000/docs`
  - ReDoc: `http://localhost:8000/redoc`
- **健康检查**: `http://localhost:8000/health`

**支持的内容类型**

- `application/json` - JSON 数据
- `multipart/form-data` - 文件上传

---

## 🔍 RAG 文档索引模块

### 1. 建立文档索引

**端点**: `POST /api/v1/rag/index`

**描述**: 为上传的文档建立向量索引，用于后续的智能问答

**请求格式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `file` | File | ✅ | 文档文件（.md/.txt） |
| `course_id` | String | ✅ | 课程 ID |
| `course_material_id` | String | ✅ | 课程材料 ID |
| `collection_name` | String | ❌ | 集合名称（默认使用配置） |

**cURL 示例**:

```bash
curl -X POST "http://localhost:8000/api/v1/rag/index" \
  -F "file=@python基础.md" \
  -F "course_id=CS101" \
  -F "course_material_id=001" \
  -F "collection_name=course_materials"
```

**响应示例**:

```json
{
  "success": true,
  "message": "文档索引建立成功",
  "document_count": 1,
  "chunk_count": 15,
  "processing_time": 8.5,
  "collection_name": "course_materials"
}
```

### 2. 获取集合列表

**端点**: `GET /api/v1/rag/collections`

**描述**: 获取所有向量集合的列表

**响应示例**:

```json
{
  "collections": [
    {
      "name": "course_materials",
      "vectors_count": 1250,
      "indexed_only": false,
      "payload_schema": {
        "course_id": "keyword",
        "course_material_id": "keyword"
      }
    }
  ],
  "total_count": 1
}
```

### 3. 删除集合

**端点**: `DELETE /api/v1/rag/collections/{collection_name}`

**描述**: 删除指定的向量集合

**路径参数**:
| 参数名 | 类型 | 描述 |
|--------|------|------|
| `collection_name` | String | 集合名称 |

**响应示例**:

```json
{
  "success": true,
  "message": "集合删除成功",
  "collection_name": "course_materials"
}
```

---

## 💬 基于RAG的智能聊天

### 1. 智能对话

**端点**: `POST /api/v1/conversation/chat`

**描述**: 与 AI 进行智能对话，支持基于文档的问答和自由聊天

**请求格式**: `application/json`

**请求参数**:
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `conversation_id` | String | ✅ | 会话 ID |
| `question` | String | ✅ | 用户问题 |
| `chat_engine_type` | String | ✅ | 引擎类型 |
| `course_id` | String | ❌ | 课程 ID（与 course_material_id 二选一） |
| `course_material_id` | String | ❌ | 材料 ID（与 course_id 二选一） |
| `collection_name` | String | ❌ | 集合名称 |

**聊天引擎类型**:

- `condense_plus_context`: 检索增强模式，基于文档内容回答
- `simple`: 直接对话模式，不检索文档

**请求示例**:

```json
{
  "conversation_id": "user123_session1",
  "question": "Python中的变量是什么？",
  "chat_engine_type": "condense_plus_context",
  "course_id": "CS101"
}
```

**响应示例**:

```json
{
  "answer": "Python中的变量是用来存储数据的容器。变量可以存储不同类型的数据，如数字、字符串、列表等...",
  "sources": [
    {
      "content": "变量是Python编程的基础概念...",
      "metadata": {
        "course_id": "CS101",
        "course_material_id": "001",
        "file_path": "data/uploads/CS101/001_Python基础.md"
      },
      "score": 0.85
    }
  ],
  "conversation_id": "user123_session1",
  "chat_engine_type": "condense_plus_context",
  "filter_info": "过滤条件: course_id=CS101",
  "processing_time": 2.3
}
```

### 2. 清除会话记录

**端点**: `DELETE /api/v1/conversation/conversations/{conversation_id}`

**描述**: 清除指定会话的聊天记录

**路径参数**:
| 参数名 | 类型 | 描述 |
|--------|------|------|
| `conversation_id` | String | 会话 ID |

**响应示例**:

```json
{
  "success": true,
  "message": "会话记录已清除",
  "conversation_id": "user123_session1",
  "cleared_messages": 15
}
```

### 3. 获取会话状态

**端点**: `GET /api/v1/conversation/conversations/{conversation_id}/status`

**描述**: 获取指定会话的状态信息

**路径参数**:
| 参数名 | 类型 | 描述 |
|--------|------|------|
| `conversation_id` | String | 会话 ID |

**响应示例**:

```json
{
  "conversation_id": "user123_session1",
  "exists": true,
  "message_count": 10,
  "last_activity": "2024-08-22T10:30:00",
  "memory_usage": {
    "token_count": 2500,
    "summary": "用户询问了Python基础概念..."
  }
}
```

### 4. 获取可用引擎

**端点**: `GET /api/v1/conversation/engines`

**描述**: 获取所有可用的聊天引擎类型和配置信息

**响应示例**:

```json
{
  "engines": [
    {
      "type": "condense_plus_context",
      "name": "检索增强模式",
      "description": "基于文档内容的智能问答，适合知识查询",
      "features": [
        "向量检索",
        "上下文整合",
        "来源追踪",
        "动态过滤",
        "问题压缩",
        "对话记忆"
      ],
      "use_cases": ["课程内容问答", "文档知识查询", "专业领域咨询"]
    },
    {
      "type": "simple",
      "name": "直接对话模式",
      "description": "与AI直接对话，不检索文档，适合一般聊天",
      "features": ["快速响应", "对话记忆", "自然交流", "多轮对话"],
      "use_cases": ["一般性聊天", "创意讨论", "问题澄清"]
    }
  ],
  "configuration": {
    "memory_management": "Redis-based chat store",
    "prompt_templates": "File-based templates",
    "vector_search": "Qdrant vector database",
    "llm_backend": "OpenAI compatible API"
  }
}
```

### 5. 获取对话配置

**端点**: `GET /api/v1/conversation/config`

**描述**: 获取对话服务的配置信息

**响应示例**:

```json
{
  "configuration": {
    "qdrant_url": "http://localhost:6333",
    "redis_url": "redis://localhost:6379",
    "embedding_model": "text-embedding-3-small",
    "llm_model": "gpt-4o-mini"
  },
  "components": {
    "qdrant": "connected",
    "redis": "connected",
    "openai": "configured"
  },
  "supported_engines": ["condense_plus_context", "simple"]
}
```

### 6. 聊天服务健康检查

**端点**: `GET /api/v1/conversation/health`

**描述**: 检查聊天服务的健康状态

**响应示例**:

```json
{
  "status": "healthy",
  "components": {
    "rag_config": "initialized",
    "memory_store": "connected",
    "chat_engines": "available"
  },
  "active_conversations": 25,
  "total_messages": 1250
}
```

---

## 🎯 统一课程材料处理（包含RAG索引）

### 1. 一站式材料处理

**端点**: `POST /api/v1/course-materials/process`

**描述**: 统一处理课程材料，自动完成文件上传、大纲生成、RAG 索引建立的全流程

**请求格式**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `file` | File | ✅ | 课程材料文件（.md/.txt） |
| `course_id` | String | ✅ | 课程 ID |
| `course_material_id` | String | ✅ | 课程材料 ID |
| `material_name` | String | ✅ | 材料名称 |
| `enable_rag_indexing` | Boolean | ❌ | 是否建立 RAG 索引（默认 true） |
| `rag_collection_name` | String | ❌ | RAG 集合名称 |

**响应示例**:

```json
{
  "task_id": "12345678-1234-1234-1234-123456789012",
  "status": "completed",
  "message": "课程材料处理完成",
  "rag_index_status": "completed",
  "rag_collection_name": "course_materials",
  "rag_document_count": 15,
  "processing_steps": [
    {
      "step_name": "rag_indexing",
      "status": "completed",
      "message": "RAG索引建立成功",
      "processing_time": 17.6
    }
  ]
}
```

---

## ⚠️ 错误处理

### 常见RAG相关错误码

| HTTP 状态码 | 错误类型                 | 描述                | 解决方案                         |
| ----------- | ------------------------ | ------------------- | -------------------------------- |
| 400         | `validation_error`       | 请求参数验证失败    | 检查请求参数格式和必需字段       |
| 400         | `file_validation_error`  | 文件验证失败        | 确认文件格式(.md/.txt)和大小限制 |
| 404         | `collection_not_found`   | 集合不存在          | 检查集合名称是否正确             |
| 404         | `conversation_not_found` | 会话不存在          | 检查 conversation_id 是否正确    |
| 500         | `qdrant_error`           | 向量数据库错误      | 检查 Qdrant 服务状态             |
| 500         | `redis_error`            | Redis 连接错误      | 检查 Redis 服务状态              |

---

## 📊 API 限制

### RAG相关限制

| 项目     | 限制      | 说明                 |
| -------- | --------- | -------------------- |
| 文件大小 | 10MB      | 单个文件最大大小     |
| 文件格式 | .md, .txt | 支持的文件类型       |
| 向量维度 | 1536      | 嵌入向量维度         |
| 检索数量 | 10 个     | 单次检索的文档数量   |
| 会话数量 | 1000 个   | 同时活跃的会话数     |

### 频率限制

| 端点类型 | 限制   | 时间窗口 |
| -------- | ------ | -------- |
| RAG索引  | 5 次   | 每分钟   |
| 聊天对话 | 60 次  | 每分钟   |
| 查询操作 | 100 次 | 每分钟   |

---

## 🛠️ RAG SDK 示例

### JavaScript SDK

```javascript
class RAGClient {
  constructor(baseUrl = "http://localhost:8000") {
    this.baseUrl = baseUrl;
  }

  // 建立RAG索引
  async createIndex(file, courseId, materialId, collectionName = null) {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("course_id", courseId);
    formData.append("course_material_id", materialId);
    if (collectionName) {
      formData.append("collection_name", collectionName);
    }

    const response = await fetch(`${this.baseUrl}/api/v1/rag/index`, {
      method: "POST",
      body: formData,
    });

    return await response.json();
  }

  // RAG聊天
  async ragChat(conversationId, question, courseId = null, materialId = null) {
    const data = {
      conversation_id: conversationId,
      question: question,
      chat_engine_type: "condense_plus_context"
    };

    if (courseId) data.course_id = courseId;
    if (materialId) data.course_material_id = materialId;

    const response = await fetch(`${this.baseUrl}/api/v1/conversation/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return await response.json();
  }

  // 获取集合列表
  async getCollections() {
    const response = await fetch(`${this.baseUrl}/api/v1/rag/collections`);
    return await response.json();
  }
}

// 使用示例
const ragClient = new RAGClient();

// 建立索引
const indexResult = await ragClient.createIndex(
  fileInput.files[0],
  "CS101",
  "001",
  "course_materials"
);

// RAG聊天
const chatResponse = await ragClient.ragChat(
  "user123_session1",
  "Python中的变量是什么？",
  "CS101"
);
```

### Python SDK

```python
import requests
from typing import Optional, Dict, Any

class RAGClient:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def create_index(self, file_path: str, course_id: str,
                    material_id: str, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """建立RAG索引"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'course_id': course_id,
                'course_material_id': material_id,
            }
            if collection_name:
                data['collection_name'] = collection_name

            response = self.session.post(
                f"{self.base_url}/api/v1/rag/index",
                files=files,
                data=data
            )

        return response.json()

    def rag_chat(self, conversation_id: str, question: str,
                course_id: Optional[str] = None,
                material_id: Optional[str] = None) -> Dict[str, Any]:
        """RAG聊天"""
        data = {
            "conversation_id": conversation_id,
            "question": question,
            "chat_engine_type": "condense_plus_context"
        }

        if course_id:
            data["course_id"] = course_id
        if material_id:
            data["course_material_id"] = material_id

        response = self.session.post(
            f"{self.base_url}/api/v1/conversation/chat",
            json=data
        )

        return response.json()

    def get_collections(self) -> Dict[str, Any]:
        """获取集合列表"""
        response = self.session.get(f"{self.base_url}/api/v1/rag/collections")
        return response.json()

# 使用示例
rag_client = RAGClient()

# 建立索引
index_result = rag_client.create_index(
    "python基础.md",
    "CS101",
    "001",
    "course_materials"
)

# RAG聊天
chat_response = rag_client.rag_chat(
    "user123_session1",
    "Python中的变量是什么？",
    course_id="CS101"
)
```

---

## 📚 RAG最佳实践

### 1. 文档索引最佳实践

- **文档质量**: 确保文档内容结构清晰，便于向量化
- **分块策略**: 系统会自动将文档分块，建议单个文档不超过10MB
- **集合管理**: 合理规划集合名称，便于后续管理和查询
- **索引更新**: 文档更新后需要重新建立索引

### 2. RAG聊天最佳实践

- **引擎选择**: 使用`condense_plus_context`进行基于文档的问答
- **过滤条件**: 合理使用course_id或material_id进行精确检索
- **会话管理**: 使用有意义的conversation_id便于追踪
- **问题质量**: 提出具体、明确的问题以获得更好的检索结果

### 3. 性能优化建议

- **批量索引**: 对于多个文档，建议分批处理避免超时
- **缓存策略**: 对于频繁查询的内容，考虑实施缓存机制
- **监控指标**: 定期检查向量数据库的性能指标
- **资源管理**: 合理配置Qdrant和Redis的资源分配

---

**🚀 开始使用 RAG API，构建您的智能问答系统！**
